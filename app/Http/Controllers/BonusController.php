<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Exceptions\Exception;
use App\Exceptions\InvalidArgumentException;
use App\Http\Requests\ActiveWelcomeBonusesRequest;
use App\Http\Requests\BetbySearchRequest;
use App\Http\Requests\BonusApplyRequest;
use App\Http\Requests\BonusApplyToPlayerRequest;
use App\Http\Requests\BonusCreateRequest;
use App\Http\Requests\BonusSearchRequest;
use App\Http\Requests\BetbyBonusCreateRequest;
use App\Http\Requests\EnabledBonusesRequest;
use App\Http\Requests\GetBonusAvailableForSlotsRequest;
use App\Http\Requests\GetBonusRelatedToSlotRequest;
use App\Http\Requests\IsGeneralBonusRequest;
use App\Http\Requests\IsWagerPayableRequest;
use App\Http\Requests\RegistrationPlayerBonusRequest;
use App\Http\Resources\BonusActiveWelcomeForLandResource;
use App\Http\Resources\BonusActiveWelcomeResource;
use App\Http\Resources\BonusCrudResource;
use App\Http\Resources\BonusInfoResource;
use App\Http\Resources\BonusForPlayerResource;
use App\Http\Resources\BonusPlayerWithBonusResource;
use App\Http\Resources\BonusShowResource;
use App\Http\Resources\BonusUserApplyResource;
use App\Models\Bonus;
use App\Services\BonusInfoService;
use App\Services\BonusPlayerService;
use App\Services\BonusService;
use App\Services\Dto\ActiveWelcomeBonusesDto;
use App\Services\Dto\BetbySearchDTO;
use App\Services\Dto\BonusApplyDTO;
use App\Services\Dto\BonusAvailableForSlotsDto;
use App\Services\Dto\BonusCrudDto;
use App\Services\Dto\BetbyBonusDTO;
use App\Services\Dto\BonusSearchDto;
use App\Services\Dto\EnabledBonusesDTO;
use App\Services\Dto\IsWagerPayableDTO;
use App\Services\Dto\PlayerDto;
use App\Services\Dto\RegistrationPlayerBonusDTO;
use App\Validation\ValidateUpdateUsedBonus;
use App\Services\PlayerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

/**
 * Class BonusController
 */
class BonusController extends Controller
{
    public function __construct(
        private readonly BonusService $bonusService,
        private readonly BonusInfoService $bonusInfoService,
        private readonly BonusPlayerService $bonusPlayerService,
        private readonly PlayerService $playerService,
    ) {
    }

    /**
     * @throws Exception
     */
    public function create(BonusCreateRequest $request): BonusCrudResource
    {
        /** @var BonusCrudDto $dto */
        $dto = BonusCrudDto::fromArray($request->validated());

        return BonusCrudResource::make($this->bonusService->createOrUpdate($dto));
    }

    public function show(Bonus $bonus): BonusShowResource
    {
        $bonus->load(['bonusSlots', 'bonusSlotProviders']);

        return BonusShowResource::make($bonus);
    }

    /**
     * @throws Exception
     */
    public function update(BonusCreateRequest $request, ValidateUpdateUsedBonus $updateValidator): BonusCrudResource
    {
        /** @var BonusCrudDto $dto */
        $dto = BonusCrudDto::fromArray($request->validated());
        $updateValidator->validate($dto);

        return BonusCrudResource::make($this->bonusService->createOrUpdate($dto));
    }

    public function list(BonusSearchRequest $request): AnonymousResourceCollection
    {
        /** @var BonusSearchDto $dto */
        $dto = BonusSearchDto::fromArray($request->validated());

        return BonusCrudResource::collection($this->bonusService->searchFilteredBonusesForBonusInfoPage($dto));
    }

    public function cachedList()
    {

    }

    public function activeWelcomeBonuses(ActiveWelcomeBonusesRequest $request): AnonymousResourceCollection
    {
        $data = $request->validated();
        if (isset($data['welcome_bonus']) && !is_numeric($data['welcome_bonus'])) {
            $data['welcome_bonus'] = null;
        }
        /** @var ActiveWelcomeBonusesDto $dto */
        $dto = ActiveWelcomeBonusesDto::fromArray($data);

        return BonusActiveWelcomeResource::collection($this->bonusService->activeWelcomeBonuses($dto));
    }

    public function activeWelcomeForLandBonuses(ActiveWelcomeBonusesRequest $request): AnonymousResourceCollection
    {
        $data = $request->validated();
        if (isset($data['welcome_bonus']) && !is_numeric($data['welcome_bonus'])) {
            $data['welcome_bonus'] = null;
        }
        /** @var ActiveWelcomeBonusesDto $dto */
        $dto = ActiveWelcomeBonusesDto::fromArray($data);

        return BonusActiveWelcomeForLandResource::collection($this->bonusService->activeWelcomeBonuses($dto));
    }

    public function enabledBonuses(EnabledBonusesRequest $request): AnonymousResourceCollection
    {
        $data = $request->validated();
        if (isset($data['welcome_bonus']) && !is_numeric($data['welcome_bonus'])) {
            $data['welcome_bonus'] = null;
        }
        /** @var EnabledBonusesDTO $dto */
        $dto = EnabledBonusesDTO::fromArray($data);

        return BonusForPlayerResource::collection($this->bonusService->enabledBonuses($dto));
    }

    /**
     * @throws InvalidArgumentException
     */
    public function apply(BonusApplyRequest $request): BonusUserApplyResource
    {
        /** @var BonusApplyDTO $dto */
        $dto = BonusApplyDTO::fromArray($request->validated());

        $playerDto = $this->playerService->getPlayerByUUID($dto->playerUuid);

        return BonusUserApplyResource::make($this->bonusService->apply($dto, $playerDto));
    }

    /**
     * @throws InvalidArgumentException
     */
    public function applyToPlayer(BonusApplyToPlayerRequest $request): BonusUserApplyResource
    {
        /** @var BonusApplyDTO $dto */
        $dto = BonusApplyDTO::fromArray($request->validated());

        $playerDto = PlayerDto::fromArray([
            'id' => $request->player_id,
            'uuid' => $request->player_uuid,
            'currency' => $request->player_currency,
        ]);

        return BonusUserApplyResource::make($this->bonusService->apply($dto, $playerDto));
    }

    /**
     * @throws \Exception
     */
    public function getTemplates(BetbySearchRequest $request): JsonResponse
    {
        /** @var BetbySearchDTO $dto */
        $dto = BetbySearchDTO::fromArray($request->validated());

        return $this->success($this->bonusService->getBetbyTemplates($dto) ?? []);
    }

    public function getBonusInfo(): AnonymousResourceCollection
    {
        return BonusInfoResource::collection($this->bonusInfoService->getActiveBonusesInfoData());
    }

    public function destroy(Bonus $bonus): void
    {
        if (app()->environment(['local', 'dev'])) {
            $this->bonusService->delete($bonus);
        }
    }

    /**
     * @throws Exception
     */
    public function createBetbyBonus(BetbyBonusCreateRequest $request): BonusCrudResource
    {
        /** @var BetbyBonusDTO $dto */
        $dto = BetbyBonusDTO::fromArray($request->validated());

        return BonusCrudResource::make($this->bonusService->createBetbyBonus($dto));
    }

    public function detach(Request $request): JsonResponse
    {
        $this->bonusService->detach((int)$request->get('playerId'));

        Log::info('Detach logs', [
            'player_uuid' => $request->get('player_uuid'),
        ]);

        return $this->success([
            'status' => 'success',
        ]);
    }

    /**
     * @throws Exception
     */
    public function registrationPlayerBonus(RegistrationPlayerBonusRequest $request): JsonResponse
    {
        /** @var RegistrationPlayerBonusDTO $dto */
        $dto = RegistrationPlayerBonusDTO::fromArray($request->validated());
        $this->bonusService->registrationPlayerBonus($dto);

        return $this->success([
            'status' => 'success',
        ]);
    }

    /**
     * @throws ModelNotFoundException
     */
    public function getPlayerSelectedBonusByUuid(string $playerUuid): BonusForPlayerResource
    {
        $bonus = $this->bonusPlayerService->getSelectedBonusByPlayerUuid($playerUuid);

        return BonusForPlayerResource::make($bonus);
    }

    public function isBonusRelatedToSlot(GetBonusRelatedToSlotRequest $request): JsonResponse
    {
        return $this->success([
            'is_bonus_related_to_slot' => $this->bonusService
                ->isBonusRelatedToSlot((int)$request->bonus_id, (int)$request->slot_id),
        ]);
    }

    public function bonusesIdsBySlotId(int $slotId): JsonResponse
    {
        return $this->success([
            'ids' => $this->bonusService->getBonusesIdsBySlotId($slotId),
        ]);
    }

    public function slotProviderIdsByBonusId(Request $request): JsonResponse
    {
        $bonusId = $request->route('bonusId');
        if (!is_numeric($bonusId)) {
            return response()->json(['message' => 'Invalid bonus id'], 422);
        }

        return $this->success([
            'ids' => $this->bonusService->getBonusSlotProviderIds((int)$bonusId),
        ]);
    }

    public function isWagerPayable(IsWagerPayableRequest $request): JsonResponse
    {
        $dto = IsWagerPayableDTO::fromArray($request->validated());

        return $this->success([
            'is_wager_payable' => $this->bonusService->isWagerPayable($dto),
        ]);
    }

    public function isGeneralBonus(IsGeneralBonusRequest $request): JsonResponse
    {
        return $this->success([
            'is_general_bonus' => $this->bonusService->isGeneral($request->bonus_id),
        ]);
    }

    public function getBonusIdsByGenreId(int $genreId): JsonResponse
    {
        return $this->success([
            'ids' => $this->bonusService->getBonusIdsByGenreId($genreId),
        ]);
    }

    public function isBonusAvailableForSlots(GetBonusAvailableForSlotsRequest $request): JsonResponse
    {
        $dto = BonusAvailableForSlotsDto::fromArray($request->validated());

        return $this->success([
            'is_available' => $this->bonusService->isBonusAvailableByGenreIdOrSlotId($dto),
        ]);
    }

    public function getMySelectedBonus(Request $request): BonusPlayerWithBonusResource
    {
        $bonus = $this->bonusPlayerService->getSelectedBonusPlayerDataOrFail(
            (int)$request->get('playerId')
        );

        return BonusPlayerWithBonusResource::make($bonus);
    }
}
