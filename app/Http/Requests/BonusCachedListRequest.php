<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class BonusCachedListRequest
 *
 * @property int[] $bonus_ids
 */
class BonusCachedListRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'bonus_ids' => [
                'nullable',
                'array',
                'min:1',
                'max:100',
                'required_without_all:type,casino,active,is_organic,external_id,genre_id',
            ],
            'bonus_ids.*' => ['required', 'integer', 'min:1'],
            'type' => ['nullable', 'string'],
            'casino' => ['nullable', 'boolean'],
            'active' => ['nullable', 'boolean'],
            'is_organic' => ['nullable', 'boolean'],
            'external_id' => ['nullable', 'integer'],
            'genre_id' => ['nullable', 'string'],
        ];
    }
}
