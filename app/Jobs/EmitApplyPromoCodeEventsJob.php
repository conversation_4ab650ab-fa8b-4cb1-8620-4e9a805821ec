<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Clients\AppClient;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Log;

class EmitApplyPromoCodeEventsJob extends Job
{
    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly int $playerId,
        private readonly string $promoCode
    ) {
    }

    /**
     * Execute the job.
     *
     * @throws ConnectionException
     */
    public function handle(AppClient $appClient): void
    {
        $result = $appClient->emitApplyPromoCodeEvents($this->playerId, $this->promoCode);

        Log::info('ApplyPromoCode Events emitted', [
            'result' => $result,
        ]);
    }
}
