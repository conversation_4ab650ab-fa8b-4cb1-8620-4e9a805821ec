<?php

declare(strict_types=1);

namespace App\Services\Dto;

use App\Dto\BaseDTO;

/**
 * Class BonusCachedListDto
 *
 * @property null|int[] $bonusIds
 * @property null|string $type
 * @property null|bool $casino
 * @property null|bool $active
 * @property null|bool $is_organic
 * @property null|int $external_id
 * @property null|string $genre_id
 */
class BonusListFilterDto extends BaseDTO
{
    /**
     * @var int[]
     */
    public ?array $bonusIds = null;
    public ?string $type = null;
    public ?bool $casino = null;
    public ?bool $active = null;
    public ?bool $is_organic = null;
    public ?int $external_id = null;
    public ?string $genre_id = null;
}
